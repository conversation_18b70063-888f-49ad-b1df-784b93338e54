namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents a telemetry event sent by client applications
    /// </summary>
    public class TelemetryEvent
    {
        /// <summary>
        /// Name of the event (e.g., "Command Started", "Processing Completed")
        /// </summary>
        public string EventName { get; set; } = string.Empty;

        /// <summary>
        /// Custom properties associated with the event
        /// </summary>
        public Dictionary<string, string>? Properties { get; set; }

        /// <summary>
        /// Custom metrics associated with the event
        /// </summary>
        public Dictionary<string, double>? Metrics { get; set; }

        /// <summary>
        /// Optional user identifier
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Optional session identifier
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Timestamp of the event (UTC)
        /// </summary>
        public DateTime? Timestamp { get; set; }
    }
}

