/**
 * BecaTracktion Telemetry Client for JavaScript
 * Can be used in Node.js or browser environments
 */

class TelemetryClient {
    /**
     * Initialize the telemetry client
     * @param {string} baseUrl - Base URL of the telemetry service
     * @param {string} userId - Optional user identifier
     */
    constructor(baseUrl, userId = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.userId = userId || (typeof process !== 'undefined' ? process.env.USER : 'unknown');
        this.sessionId = this.generateUuid();
        this.timeout = 10000;
    }

    /**
     * Generate a UUID
     * @returns {string} UUID
     */
    generateUuid() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * Make an HTTP POST request
     * @param {string} endpoint - API endpoint
     * @param {object} payload - Request payload
     * @returns {Promise<boolean>} Success status
     */
    async post(endpoint, payload) {
        try {
            // For Node.js environment
            if (typeof fetch === 'undefined') {
                const https = require('https');
                const http = require('http');
                const url = require('url');
                
                return new Promise((resolve, reject) => {
                    const parsedUrl = url.parse(`${this.baseUrl}${endpoint}`);
                    const protocol = parsedUrl.protocol === 'https:' ? https : http;
                    
                    const options = {
                        hostname: parsedUrl.hostname,
                        port: parsedUrl.port,
                        path: parsedUrl.path,
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        timeout: this.timeout
                    };
                    
                    const req = protocol.request(options, (res) => {
                        resolve(res.statusCode === 200);
                    });
                    
                    req.on('error', (error) => {
                        console.error(`Error: ${error.message}`);
                        resolve(false);
                    });
                    
                    req.write(JSON.stringify(payload));
                    req.end();
                });
            } else {
                // For browser environment
                const response = await fetch(`${this.baseUrl}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload),
                });
                
                return response.ok;
            }
        } catch (error) {
            console.error(`Error making request: ${error.message}`);
            return false;
        }
    }

    /**
     * Track a custom event
     * @param {string} eventName - Name of the event
     * @param {object} properties - Optional properties
     * @param {object} metrics - Optional metrics
     * @returns {Promise<boolean>} Success status
     */
    async trackEvent(eventName, properties = {}, metrics = null) {
        const payload = {
            eventName: eventName,
            properties: properties,
            metrics: metrics,
            userId: this.userId,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString()
        };

        return await this.post('/api/telemetry/event', payload);
    }

    /**
     * Track an exception
     * @param {Error} error - The error to track
     * @param {string} location - Optional location
     * @param {object} properties - Optional properties
     * @returns {Promise<boolean>} Success status
     */
    async trackException(error, location = null, properties = {}) {
        const payload = {
            message: error.message,
            exceptionType: error.name,
            stackTrace: error.stack,
            location: location,
            properties: properties,
            userId: this.userId,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString()
        };

        return await this.post('/api/telemetry/exception', payload);
    }

    /**
     * Track a metric
     * @param {string} metricName - Name of the metric
     * @param {number} value - Metric value
     * @param {object} properties - Optional properties
     * @returns {Promise<boolean>} Success status
     */
    async trackMetric(metricName, value, properties = null) {
        const payload = {
            metricName: metricName,
            value: value,
            properties: properties,
            userId: this.userId,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString()
        };

        return await this.post('/api/telemetry/metric', payload);
    }

    /**
     * Track a page view
     * @param {string} pageName - Name of the page
     * @param {object} properties - Optional properties
     * @returns {Promise<boolean>} Success status
     */
    async trackPageView(pageName, properties = null) {
        const payload = {
            pageName: pageName,
            properties: properties,
            userId: this.userId,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString()
        };

        return await this.post('/api/telemetry/pageview', payload);
    }

    /**
     * Flush telemetry to ensure it's sent
     * @returns {Promise<boolean>} Success status
     */
    async flush() {
        return await this.post('/api/telemetry/flush', {});
    }
}

// Example usage for Node.js
async function exampleUsageNode() {
    const telemetry = new TelemetryClient('http://localhost:5004');

    // Track script start
    await telemetry.trackEvent('Node.js Script Started', {
        script_name: 'example.js',
        node_version: process.version
    });

    try {
        const startTime = Date.now();

        // Your script logic here
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Track metric
        const duration = (Date.now() - startTime) / 1000;
        await telemetry.trackMetric('Script Duration', duration);

        // Track success
        await telemetry.trackEvent('Node.js Script Completed', {
            status: 'success',
            duration: duration.toFixed(2)
        });

    } catch (error) {
        // Track exception
        await telemetry.trackException(error, 'exampleUsageNode', {
            script_name: 'example.js'
        });

        await telemetry.trackEvent('Node.js Script Failed', {
            error: error.message
        });
    } finally {
        // Ensure telemetry is sent
        await telemetry.flush();
    }
}

// Example usage for browser
async function exampleUsageBrowser() {
    const telemetry = new TelemetryClient('http://localhost:5004', 'browser-user');

    // Track page view
    await telemetry.trackPageView('Home Page', {
        url: window.location.href,
        referrer: document.referrer
    });

    // Track button click event
    document.getElementById('myButton')?.addEventListener('click', async () => {
        await telemetry.trackEvent('Button Clicked', {
            button_id: 'myButton',
            page: window.location.pathname
        });
    });

    // Track errors
    window.addEventListener('error', async (event) => {
        await telemetry.trackException(event.error, window.location.pathname, {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno.toString()
        });
    });
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TelemetryClient;
}

// Run example if executed directly
if (typeof require !== 'undefined' && require.main === module) {
    exampleUsageNode();
}

