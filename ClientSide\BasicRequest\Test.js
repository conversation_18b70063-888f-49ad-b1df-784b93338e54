//process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'; // disable SSL verification for now, remove this line in production

fetch('https://tracktion.azurewebsites.net/api/telemetry/event', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        eventName: 'Test JavaScript Event',
        properties: {
            'Model Name': 'NDH JS',
            'User Name': 'FU1'
        },
        userId: 'Le Corbusier'
    })
})
.then(res => res.json())
.then(data => console.log(data));
