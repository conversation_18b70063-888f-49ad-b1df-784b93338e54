namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents a page view telemetry event
    /// </summary>
    public class TelemetryPageView
    {
        /// <summary>
        /// Name of the page/view (e.g., "MainWindow", "SettingsDialog")
        /// </summary>
        public string PageName { get; set; } = string.Empty;

        /// <summary>
        /// Custom properties associated with the page view
        /// </summary>
        public Dictionary<string, string>? Properties { get; set; }

        /// <summary>
        /// Custom metrics associated with the page view
        /// </summary>
        public Dictionary<string, double>? Metrics { get; set; }

        /// <summary>
        /// Optional user identifier
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Optional session identifier
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Timestamp of the page view (UTC)
        /// </summary>
        public DateTime? Timestamp { get; set; }
    }
}

