using BecaTracktion.Models;

namespace BecaTracktion.Services
{
    /// <summary>
    /// Interface for telemetry service operations
    /// </summary>
    public interface ITelemetryService
    {
        /// <summary>
        /// Track a custom event
        /// </summary>
        Task TrackEventAsync(TelemetryEvent telemetryEvent);

        /// <summary>
        /// Track an exception
        /// </summary>
        Task TrackExceptionAsync(TelemetryException telemetryException);

        /// <summary>
        /// Track a metric
        /// </summary>
        Task TrackMetricAsync(TelemetryMetric telemetryMetric);

        /// <summary>
        /// Track a page view
        /// </summary>
        Task TrackPageViewAsync(TelemetryPageView telemetryPageView);

        /// <summary>
        /// Flush telemetry data to ensure it's sent
        /// </summary>
        Task FlushAsync();
    }
}

