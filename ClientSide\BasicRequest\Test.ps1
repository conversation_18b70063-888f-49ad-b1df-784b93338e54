$body = @{
    eventName = "Test PowerShell Event"
    properties = @{
        'Model Name' = 'MBS PS1'
        'User Name' = 'FU1'
    }
    userId = $env:USERNAME
} | ConvertTo-Json -Depth 3

Invoke-RestMethod -Uri "https://tracktion.azurewebsites.net/api/telemetry/event" `
    -Method Post `
    -Body $body `
    -ContentType "application/json"

# Run this when there's a permission error
# Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass