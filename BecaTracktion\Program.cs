using BecaTracktion.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Register telemetry service
builder.Services.AddSingleton<ITelemetryService, ApplicationInsightsTelemetryService>();

// Add CORS support for cross-platform clients
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "TRACKTION - Telemetry Microservice",
        Version = "v1",
        Description = "A lightweight, language-agnostic HTTP-based service designed to forward telemetry data to Azure Application Insights." +
        "The service supports multiple technologies out of the box, including .NET, JavaScript, Python, PowerShell, and Curl, allowing seamless integration across a wide range of platforms.",
        //Contact = new Microsoft.OpenApi.Models.OpenApiContact
        //{
        //    Name = "Beca",
        //    Email = "<EMAIL>"
        //}
    });

    // Include XML comments if available
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// Configure the HTTP request pipeline.
// Enable Swagger in all environments (including Production) for API testing
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "BecaTracktion Telemetry API v1");
    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
});

app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthorization();

app.MapControllers();

app.Run();
