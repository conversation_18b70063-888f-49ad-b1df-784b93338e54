namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents an exception telemetry event
    /// </summary>
    public class TelemetryException
    {
        /// <summary>
        /// Exception message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Exception type (e.g., "System.NullReferenceException")
        /// </summary>
        public string? ExceptionType { get; set; }

        /// <summary>
        /// Stack trace of the exception
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// Location where the exception occurred
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// Custom properties associated with the exception
        /// </summary>
        public Dictionary<string, string>? Properties { get; set; }

        /// <summary>
        /// Custom metrics associated with the exception
        /// </summary>
        public Dictionary<string, double>? Metrics { get; set; }

        /// <summary>
        /// Optional user identifier
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Optional session identifier
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Timestamp of the exception (UTC)
        /// </summary>
        public DateTime? Timestamp { get; set; }
    }
}

