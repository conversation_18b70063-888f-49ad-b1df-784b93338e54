# BecaTracktion Telemetry Microservice

A lightweight HTTP-based microservice for forwarding telemetry data to Azure Application Insights. This service enables legacy or host-constrained applications (e.g., Revit add-ins) to send telemetry without directly integrating platform-specific SDKs, avoiding dependency conflicts and runtime limitations.

## Architecture

```
┌─────────────────────┐
│  Client Apps        │
│  - Revit Add-ins    │
│  - Python Scripts   │
│  - JavaScript Apps  │
│  - Any HTTP Client  │
└──────────┬──────────┘
           │ HTTP POST
           │ (JSON)
           ▼
┌─────────────────────┐
│  BecaTracktion      │
│  Microservice       │
│  (ASP.NET Core)     │
└──────────┬──────────┘
           │ Application
           │ Insights SDK
           ▼
┌─────────────────────┐
│  Azure Application  │
│  Insights           │
└─────────────────────┘
```

## Features

- **Language-Agnostic**: Any client that can make HTTP requests can send telemetry
- **Event Tracking**: Track custom events with properties and metrics
- **Exception Tracking**: Log exceptions with stack traces and context
- **Metric Tracking**: Send custom metrics for monitoring
- **Page View Tracking**: Track page/view navigation
- **Health Check**: Built-in health check endpoint
- **Swagger UI**: Interactive API documentation
- **Docker Support**: Containerized for easy deployment
- **CORS Enabled**: Cross-origin requests supported