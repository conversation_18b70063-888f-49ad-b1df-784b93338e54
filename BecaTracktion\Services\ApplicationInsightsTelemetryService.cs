using BecaTracktion.Models;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;

namespace BecaTracktion.Services
{
    /// <summary>
    /// Implementation of telemetry service using Application Insights
    /// Based on the working TelemetryHandler from Revit add-in
    /// </summary>
    public class ApplicationInsightsTelemetryService : ITelemetryService, IDisposable
    {
        private readonly TelemetryClient _telemetryClient;
        private readonly ILogger<ApplicationInsightsTelemetryService> _logger;
        private readonly TelemetryConfiguration _telemetryConfiguration;

        public ApplicationInsightsTelemetryService(
            IConfiguration configuration,
            ILogger<ApplicationInsightsTelemetryService> logger)
        {
            _logger = logger;

            // Get connection string from configuration
            var connectionString = configuration["ApplicationInsights:ConnectionString"];

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogWarning("Application Insights connection string is not configured. Telemetry will not be sent.");
                // Create a disabled telemetry configuration
                _telemetryConfiguration = new TelemetryConfiguration();
                _telemetryConfiguration.DisableTelemetry = true;
            }
            else
            {
                // Create telemetry configuration similar to the Revit add-in
                _telemetryConfiguration = new TelemetryConfiguration
                {
                    ConnectionString = connectionString
                };
            }

            _telemetryClient = new TelemetryClient(_telemetryConfiguration);

            // Set common context properties
            _telemetryClient.Context.Device.OperatingSystem = Environment.OSVersion.ToString();
            _telemetryClient.Context.Component.Version = GetType().Assembly.GetName().Version?.ToString() ?? "1.0.0";

            _logger.LogInformation("Application Insights Telemetry Service initialized");
        }

        public Task TrackEventAsync(TelemetryEvent telemetryEvent)
        {
            try
            {
                // Set context if provided
                if (!string.IsNullOrEmpty(telemetryEvent.UserId))
                {
                    _telemetryClient.Context.User.Id = telemetryEvent.UserId;
                }

                if (!string.IsNullOrEmpty(telemetryEvent.SessionId))
                {
                    _telemetryClient.Context.Session.Id = telemetryEvent.SessionId;
                }

                // Add timestamp to properties if provided
                var properties = telemetryEvent.Properties ?? new Dictionary<string, string>();
                if (telemetryEvent.Timestamp.HasValue)
                {
                    properties["Timestamp"] = telemetryEvent.Timestamp.Value.ToString("o");
                }

                // Track the event
                _telemetryClient.TrackEvent(
                    telemetryEvent.EventName,
                    properties,
                    telemetryEvent.Metrics
                );

                _logger.LogDebug("Tracked event: {EventName}", telemetryEvent.EventName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking event: {EventName}", telemetryEvent.EventName);
                throw;
            }

            return Task.CompletedTask;
        }

        public Task TrackExceptionAsync(TelemetryException telemetryException)
        {
            try
            {
                // Set context if provided
                if (!string.IsNullOrEmpty(telemetryException.UserId))
                {
                    _telemetryClient.Context.User.Id = telemetryException.UserId;
                }

                if (!string.IsNullOrEmpty(telemetryException.SessionId))
                {
                    _telemetryClient.Context.Session.Id = telemetryException.SessionId;
                }

                // Create an exception object from the telemetry data
                var exception = new Exception(telemetryException.Message);

                // Prepare properties
                var properties = telemetryException.Properties ?? new Dictionary<string, string>();
                
                if (!string.IsNullOrEmpty(telemetryException.ExceptionType))
                {
                    properties["ExceptionType"] = telemetryException.ExceptionType;
                }

                if (!string.IsNullOrEmpty(telemetryException.StackTrace))
                {
                    properties["StackTrace"] = telemetryException.StackTrace;
                }

                if (!string.IsNullOrEmpty(telemetryException.Location))
                {
                    properties["Location"] = telemetryException.Location;
                }

                if (telemetryException.Timestamp.HasValue)
                {
                    properties["Timestamp"] = telemetryException.Timestamp.Value.ToString("o");
                }

                // Track the exception
                _telemetryClient.TrackException(
                    exception,
                    properties,
                    telemetryException.Metrics
                );

                _logger.LogDebug("Tracked exception: {Message}", telemetryException.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking exception: {Message}", telemetryException.Message);
                throw;
            }

            return Task.CompletedTask;
        }

        public Task TrackMetricAsync(TelemetryMetric telemetryMetric)
        {
            try
            {
                // Set context if provided
                if (!string.IsNullOrEmpty(telemetryMetric.UserId))
                {
                    _telemetryClient.Context.User.Id = telemetryMetric.UserId;
                }

                if (!string.IsNullOrEmpty(telemetryMetric.SessionId))
                {
                    _telemetryClient.Context.Session.Id = telemetryMetric.SessionId;
                }

                // Add timestamp to properties if provided
                var properties = telemetryMetric.Properties ?? new Dictionary<string, string>();
                if (telemetryMetric.Timestamp.HasValue)
                {
                    properties["Timestamp"] = telemetryMetric.Timestamp.Value.ToString("o");
                }

                // Track the metric
                var metric = new MetricTelemetry(
                    telemetryMetric.MetricName,
                    telemetryMetric.Value
                );

                foreach (var prop in properties)
                {
                    metric.Properties[prop.Key] = prop.Value;
                }

                _telemetryClient.TrackMetric(metric);

                _logger.LogDebug("Tracked metric: {MetricName} = {Value}", telemetryMetric.MetricName, telemetryMetric.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking metric: {MetricName}", telemetryMetric.MetricName);
                throw;
            }

            return Task.CompletedTask;
        }

        public Task TrackPageViewAsync(TelemetryPageView telemetryPageView)
        {
            try
            {
                // Set context if provided
                if (!string.IsNullOrEmpty(telemetryPageView.UserId))
                {
                    _telemetryClient.Context.User.Id = telemetryPageView.UserId;
                }

                if (!string.IsNullOrEmpty(telemetryPageView.SessionId))
                {
                    _telemetryClient.Context.Session.Id = telemetryPageView.SessionId;
                }

                // Add timestamp to properties if provided
                var properties = telemetryPageView.Properties ?? new Dictionary<string, string>();
                if (telemetryPageView.Timestamp.HasValue)
                {
                    properties["Timestamp"] = telemetryPageView.Timestamp.Value.ToString("o");
                }

                // Track the page view
                var pageView = new PageViewTelemetry(telemetryPageView.PageName);

                foreach (var prop in properties)
                {
                    pageView.Properties[prop.Key] = prop.Value;
                }

                if (telemetryPageView.Metrics != null)
                {
                    foreach (var metric in telemetryPageView.Metrics)
                    {
                        pageView.Metrics[metric.Key] = metric.Value;
                    }
                }

                _telemetryClient.TrackPageView(pageView);

                _logger.LogDebug("Tracked page view: {PageName}", telemetryPageView.PageName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking page view: {PageName}", telemetryPageView.PageName);
                throw;
            }

            return Task.CompletedTask;
        }

        public async Task FlushAsync()
        {
            try
            {
                _telemetryClient.Flush();
                
                // Allow time for flushing (similar to the Revit add-in implementation)
                await Task.Delay(1000);

                _logger.LogDebug("Telemetry flushed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error flushing telemetry");
                throw;
            }
        }

        public void Dispose()
        {
            _telemetryConfiguration?.Dispose();
        }
    }
}

