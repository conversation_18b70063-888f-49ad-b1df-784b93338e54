# BecaTracktion Telemetry Microservice Architecture

## Overview

The BecaTracktion Telemetry Microservice is designed to decouple telemetry collection from host applications, particularly those with constrained environments like Revit add-ins. This architecture solves the problem of SDK dependency conflicts and version incompatibilities by providing a language-agnostic HTTP interface.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────┐
│                         Client Applications                          │
├─────────────────────────────────────────────────────────────────────┤
│                                                                       │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐              │
│  │ Revit Add-in │  │ Python Script│  │ JavaScript   │              │
│  │   (.NET)     │  │              │  │   App        │              │
│  └──────┬───────┘  └──────┬───────┘  └──────┬───────┘              │
│         │                  │                  │                      │
│         │  HTTP POST       │  HTTP POST       │  HTTP POST           │
│         │  (JSON)          │  (JSON)          │  (JSON)              │
│         └──────────────────┴──────────────────┘                      │
│                            │                                          │
└────────────────────────────┼──────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────────┐
│                    BecaTracktion Microservice                        │
│                      (ASP.NET Core Web API)                          │
├─────────────────────────────────────────────────────────────────────┤
│                                                                       │
│  ┌───────────────────────────────────────────────────────────┐      │
│  │                    API Layer (Controllers)                 │      │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐ │      │
│  │  │  Event   │  │Exception │  │  Metric  │  │PageView  │ │      │
│  │  │Endpoint  │  │Endpoint  │  │Endpoint  │  │Endpoint  │ │      │
│  │  └────┬─────┘  └────┬─────┘  └────┬─────┘  └────┬─────┘ │      │
│  └───────┼─────────────┼─────────────┼─────────────┼────────┘      │
│          │             │             │             │                │
│          └─────────────┴─────────────┴─────────────┘                │
│                            │                                          │
│  ┌─────────────────────────▼──────────────────────────────┐         │
│  │              Service Layer (Business Logic)             │         │
│  │                                                          │         │
│  │  ┌────────────────────────────────────────────────┐    │         │
│  │  │   ApplicationInsightsTelemetryService          │    │         │
│  │  │                                                 │    │         │
│  │  │  • Context Management (User, Session)          │    │         │
│  │  │  • Data Transformation                         │    │         │
│  │  │  • Error Handling                              │    │         │
│  │  │  • Telemetry Client Management                 │    │         │
│  │  └────────────────────┬───────────────────────────┘    │         │
│  └───────────────────────┼────────────────────────────────┘         │
│                          │                                            │
│  ┌───────────────────────▼────────────────────────────────┐         │
│  │         Application Insights SDK Integration            │         │
│  │                                                          │         │
│  │  • TelemetryClient                                      │         │
│  │  • TelemetryConfiguration                               │         │
│  │  • Connection String Management                         │         │
│  └──────────────────────────────────────────────────────────┘        │
│                                                                       │
└────────────────────────────┬──────────────────────────────────────────┘
                             │
                             │ HTTPS
                             │ (Application Insights Protocol)
                             ▼
┌─────────────────────────────────────────────────────────────────────┐
│                   Azure Application Insights                         │
├─────────────────────────────────────────────────────────────────────┤
│                                                                       │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐              │
│  │   Events     │  │  Exceptions  │  │   Metrics    │              │
│  │   Storage    │  │   Storage    │  │   Storage    │              │
│  └──────────────┘  └──────────────┘  └──────────────┘              │
│                                                                       │
│  ┌──────────────────────────────────────────────────────┐           │
│  │            Analytics & Monitoring                     │           │
│  │  • Dashboards  • Alerts  • Queries  • Reports        │           │
│  └──────────────────────────────────────────────────────┘           │
│                                                                       │
└─────────────────────────────────────────────────────────────────────┘
```

## Component Details

### 1. Client Applications

**Purpose**: Applications that need to send telemetry data

**Characteristics**:
- Can be written in any language (C#, Python, JavaScript, etc.)
- Only need HTTP client capabilities
- No direct dependency on Application Insights SDK
- Lightweight integration

**Examples**:
- Revit add-ins (avoiding SDK conflicts)
- Python automation scripts
- JavaScript web applications
- PowerShell scripts
- Legacy applications

### 2. BecaTracktion Microservice

#### API Layer (Controllers)

**TelemetryController**:
- `POST /api/telemetry/event` - Track custom events
- `POST /api/telemetry/exception` - Track exceptions
- `POST /api/telemetry/metric` - Track metrics
- `POST /api/telemetry/pageview` - Track page views
- `POST /api/telemetry/flush` - Force flush telemetry
- `GET /api/telemetry/health` - Health check

**Responsibilities**:
- Request validation
- HTTP response handling
- Error handling and logging
- API documentation (Swagger)

#### Service Layer

**ApplicationInsightsTelemetryService**:
- Implements `ITelemetryService` interface
- Manages TelemetryClient lifecycle
- Handles context (user, session, device)
- Transforms HTTP requests to Application Insights format
- Manages connection configuration

**Key Features**:
- Singleton service (one instance per application)
- Thread-safe operations
- Graceful error handling
- Configurable connection string

#### Models

**Request Models**:
- `TelemetryEvent` - Event data structure
- `TelemetryException` - Exception data structure
- `TelemetryMetric` - Metric data structure
- `TelemetryPageView` - Page view data structure

**Response Model**:
- `TelemetryResponse` - Standardized response format

### 3. Azure Application Insights

**Purpose**: Cloud-based telemetry storage and analytics

**Features**:
- Real-time telemetry ingestion
- Data retention and storage
- Query capabilities (Kusto Query Language)
- Dashboards and visualizations
- Alerting and monitoring
- Integration with Azure Monitor

## Data Flow

### Event Tracking Flow

```
1. Client Application
   └─> Creates event data (name, properties, metrics)
   └─> Serializes to JSON
   └─> HTTP POST to /api/telemetry/event

2. TelemetryController
   └─> Validates request
   └─> Calls ITelemetryService.TrackEventAsync()

3. ApplicationInsightsTelemetryService
   └─> Sets context (user, session)
   └─> Adds timestamp
   └─> Calls TelemetryClient.TrackEvent()

4. Application Insights SDK
   └─> Buffers telemetry
   └─> Batches requests
   └─> Sends to Azure via HTTPS

5. Azure Application Insights
   └─> Ingests data
   └─> Stores in telemetry tables
   └─> Makes available for queries
```

## Deployment Options

### 1. Local Development
```bash
dotnet run --project BecaTracktion
```
- Runs on localhost:5004
- Swagger UI available
- Development configuration

### 2. Docker Container
```bash
docker build -t becatracktion:latest .
docker run -p 8080:80 becatracktion:latest
```
- Containerized deployment
- Portable across environments
- Easy scaling

### 3. Azure App Service
- Managed hosting
- Auto-scaling
- Built-in monitoring
- SSL/TLS included

### 4. Kubernetes
- Container orchestration
- High availability
- Load balancing
- Auto-scaling

## Security Considerations

### Current Implementation
- CORS enabled for all origins (development)
- No authentication required
- Connection string in configuration

### Production Recommendations

1. **Authentication**:
   - Add API key authentication
   - Implement OAuth 2.0 / JWT tokens
   - Use Azure AD integration

2. **Authorization**:
   - Role-based access control
   - Rate limiting per client
   - Request throttling

3. **Network Security**:
   - HTTPS only (disable HTTP)
   - Restrict CORS to specific origins
   - Use Azure Private Link
   - Implement IP whitelisting

4. **Configuration**:
   - Store connection string in Azure Key Vault
   - Use managed identities
   - Environment-specific configurations

5. **Data Protection**:
   - Sanitize sensitive data
   - Implement PII filtering
   - Encrypt data in transit

## Scalability

### Horizontal Scaling
- Stateless design allows multiple instances
- Load balancer distributes requests
- No session affinity required

### Performance Optimization
- Async/await throughout
- Minimal processing overhead
- Efficient JSON serialization
- Connection pooling in HTTP clients

### Monitoring
- Built-in ASP.NET Core logging
- Application Insights for the service itself
- Health check endpoint for monitoring

## Benefits of This Architecture

1. **Decoupling**: Client apps don't need Application Insights SDK
2. **Flexibility**: Easy to change telemetry backend without updating clients
3. **Compatibility**: Works with any language/platform
4. **Maintainability**: Centralized telemetry logic
5. **Scalability**: Can handle multiple clients and high volume
6. **Reliability**: Graceful degradation if telemetry fails
7. **Version Independence**: Update service without updating clients

## Comparison with Direct Integration

| Aspect | Direct Integration | Microservice Approach |
|--------|-------------------|----------------------|
| SDK Dependency | Required in each app | Only in microservice |
| Version Conflicts | Possible | Eliminated |
| Language Support | SDK must exist | Any HTTP client |
| Configuration | Per application | Centralized |
| Updates | Redeploy all apps | Update service only |
| Testing | Complex | Simplified |
| Legacy Support | Difficult | Easy |

## Future Enhancements

1. **Batch Processing**: Accept multiple events in one request
2. **Queuing**: Add message queue for reliability
3. **Caching**: Cache frequently used data
4. **Analytics**: Built-in analytics endpoints
5. **Multi-Backend**: Support multiple telemetry backends
6. **Filtering**: Server-side filtering rules
7. **Sampling**: Intelligent sampling strategies
8. **Offline Support**: Store-and-forward capability

